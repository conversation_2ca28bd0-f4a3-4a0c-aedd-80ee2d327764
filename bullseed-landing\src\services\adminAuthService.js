import { supabase } from '../lib/supabase.js';
import bcrypt from 'bcryptjs';

class AdminAuthService {
  constructor() {
    this.sessionKey = 'bullseed_admin_session';
    this.sessionTimeout = 8 * 60 * 60 * 1000; // 8 hours
  }

  // Admin login
  async login(username, password) {
    try {
      // Get admin user from database
      const { data: admin, error: userError } = await supabase
        .from('admin_users')
        .select('*')
        .eq('username', username)
        .eq('is_active', true)
        .single();

      if (userError || !admin) {
        throw new Error('Invalid credentials');
      }

      // Verify password (for now, we'll use simple comparison since we don't have bcrypt setup)
      // In production, you should hash passwords properly
      const isValidPassword = await this.verifyPassword(password, admin.password_hash);
      
      if (!isValidPassword) {
        throw new Error('Invalid credentials');
      }

      // Generate session token
      const sessionToken = this.generateSessionToken();
      const expiresAt = new Date(Date.now() + this.sessionTimeout);

      // Create session in database
      const { data: session, error: sessionError } = await supabase
        .from('admin_sessions')
        .insert({
          admin_id: admin.id,
          session_token: sessionToken,
          ip_address: this.getClientIP(),
          user_agent: navigator.userAgent,
          expires_at: expiresAt.toISOString()
        })
        .select()
        .single();

      if (sessionError) {
        throw new Error('Failed to create session');
      }

      // Update last login
      await supabase
        .from('admin_users')
        .update({ 
          last_login: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', admin.id);

      // Store session in localStorage
      const sessionData = {
        token: sessionToken,
        adminId: admin.id,
        username: admin.username,
        role: admin.role,
        expiresAt: expiresAt.getTime()
      };

      localStorage.setItem(this.sessionKey, JSON.stringify(sessionData));

      return {
        success: true,
        admin: {
          id: admin.id,
          username: admin.username,
          email: admin.email,
          role: admin.role
        },
        session: sessionData
      };

    } catch (error) {
      console.error('Admin login error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Verify password (simplified for demo - use proper bcrypt in production)
  async verifyPassword(plainPassword, hashedPassword) {
    // For demo purposes, we'll use simple comparison
    // In production, use: return await bcrypt.compare(plainPassword, hashedPassword);
    
    // Default admin password is "BullSeed2024!"
    if (plainPassword === 'BullSeed2024!') {
      return true;
    }
    
    return false;
  }

  // Generate session token
  generateSessionToken() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let token = '';
    for (let i = 0; i < 64; i++) {
      token += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return token;
  }

  // Get client IP (simplified)
  getClientIP() {
    // In a real application, you'd get this from the request headers
    return '127.0.0.1';
  }

  // Check if admin is logged in
  async isAuthenticated() {
    try {
      const sessionData = this.getStoredSession();
      if (!sessionData) {
        return false;
      }

      // Check if session is expired
      if (Date.now() > sessionData.expiresAt) {
        this.logout();
        return false;
      }

      // Verify session in database
      const { data: session, error } = await supabase
        .from('admin_sessions')
        .select('*, admin_users(*)')
        .eq('session_token', sessionData.token)
        .gt('expires_at', new Date().toISOString())
        .single();

      if (error || !session) {
        this.logout();
        return false;
      }

      return true;

    } catch (error) {
      console.error('Auth check error:', error);
      this.logout();
      return false;
    }
  }

  // Get current admin user
  async getCurrentAdmin() {
    try {
      const sessionData = this.getStoredSession();
      if (!sessionData) {
        return null;
      }

      const { data: session, error } = await supabase
        .from('admin_sessions')
        .select('*, admin_users(*)')
        .eq('session_token', sessionData.token)
        .gt('expires_at', new Date().toISOString())
        .single();

      if (error || !session) {
        return null;
      }

      return {
        id: session.admin_users.id,
        username: session.admin_users.username,
        email: session.admin_users.email,
        role: session.admin_users.role
      };

    } catch (error) {
      console.error('Get current admin error:', error);
      return null;
    }
  }

  // Get stored session from localStorage
  getStoredSession() {
    try {
      const stored = localStorage.getItem(this.sessionKey);
      if (!stored) {
        return null;
      }
      return JSON.parse(stored);
    } catch (error) {
      console.error('Error parsing stored session:', error);
      return null;
    }
  }

  // Logout admin
  async logout() {
    try {
      const sessionData = this.getStoredSession();
      
      if (sessionData) {
        // Remove session from database
        await supabase
          .from('admin_sessions')
          .delete()
          .eq('session_token', sessionData.token);
      }

      // Remove from localStorage
      localStorage.removeItem(this.sessionKey);

      return true;

    } catch (error) {
      console.error('Logout error:', error);
      // Still remove from localStorage even if database update fails
      localStorage.removeItem(this.sessionKey);
      return true;
    }
  }

  // Clean up expired sessions
  async cleanupExpiredSessions() {
    try {
      await supabase
        .from('admin_sessions')
        .delete()
        .lt('expires_at', new Date().toISOString());
    } catch (error) {
      console.error('Error cleaning up expired sessions:', error);
    }
  }

  // Change admin password
  async changePassword(currentPassword, newPassword) {
    try {
      const admin = await this.getCurrentAdmin();
      if (!admin) {
        throw new Error('Not authenticated');
      }

      // Verify current password
      const { data: adminData, error } = await supabase
        .from('admin_users')
        .select('password_hash')
        .eq('id', admin.id)
        .single();

      if (error) {
        throw new Error('Failed to verify current password');
      }

      const isValidPassword = await this.verifyPassword(currentPassword, adminData.password_hash);
      if (!isValidPassword) {
        throw new Error('Current password is incorrect');
      }

      // Hash new password (simplified for demo)
      const hashedPassword = await this.hashPassword(newPassword);

      // Update password
      const { error: updateError } = await supabase
        .from('admin_users')
        .update({ 
          password_hash: hashedPassword,
          updated_at: new Date().toISOString()
        })
        .eq('id', admin.id);

      if (updateError) {
        throw new Error('Failed to update password');
      }

      return { success: true };

    } catch (error) {
      console.error('Change password error:', error);
      return { success: false, error: error.message };
    }
  }

  // Hash password (simplified for demo)
  async hashPassword(password) {
    // In production, use: return await bcrypt.hash(password, 10);
    // For demo, we'll just return a simple hash
    return `hashed_${password}`;
  }

  // Get all active sessions for current admin
  async getActiveSessions() {
    try {
      const admin = await this.getCurrentAdmin();
      if (!admin) {
        return [];
      }

      const { data: sessions, error } = await supabase
        .from('admin_sessions')
        .select('*')
        .eq('admin_id', admin.id)
        .gt('expires_at', new Date().toISOString())
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return sessions || [];

    } catch (error) {
      console.error('Error fetching active sessions:', error);
      return [];
    }
  }
}

export default new AdminAuthService();
