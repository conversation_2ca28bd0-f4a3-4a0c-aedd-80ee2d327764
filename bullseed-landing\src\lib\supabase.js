import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

// Database service functions
export const dbService = {
  // User functions
  async getUser(authId) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('auth_id', authId)
      .single();

    if (error) throw error;
    return data;
  },

  async updateUser(userId, updates) {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  // Transaction functions
  async getTransactions(userId, limit = 50) {
    const { data, error } = await supabase
      .from('transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);
    
    if (error) throw error;
    return data;
  },

  async createTransaction(transaction) {
    const { data, error } = await supabase
      .from('transactions')
      .insert(transaction)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async updateTransaction(transactionId, updates) {
    const { data, error } = await supabase
      .from('transactions')
      .update(updates)
      .eq('id', transactionId)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  // Investment functions
  async getInvestments(userId) {
    const { data, error } = await supabase
      .from('investments')
      .select(`
        *,
        investment_plans (
          name,
          daily_return,
          total_return,
          duration_days
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  },

  async getInvestmentPlans() {
    const { data, error } = await supabase
      .from('investment_plans')
      .select('*')
      .order('min_amount', { ascending: true });

    if (error) throw error;
    return data;
  },

  // Crypto deposit functions
  async createDeposit(depositData) {
    const { data, error } = await supabase
      .from('crypto_deposits')
      .insert(depositData)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async getDeposits(userId, limit = 50) {
    const { data, error } = await supabase
      .from('crypto_deposits')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data;
  },

  async getDeposit(depositId) {
    const { data, error } = await supabase
      .from('crypto_deposits')
      .select('*')
      .eq('id', depositId)
      .single();

    if (error) throw error;
    return data;
  },

  async updateDeposit(depositId, updates) {
    const { data, error } = await supabase
      .from('crypto_deposits')
      .update(updates)
      .eq('id', depositId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async createInvestment(investment) {
    const { data, error } = await supabase
      .from('investments')
      .insert(investment)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  // Referral functions
  async getReferralData(userId) {
    const { data, error } = await supabase
      .from('referrals')
      .select(`
        *,
        referral_earnings (
          commission_amount
        )
      `)
      .eq('referrer_id', userId);
    
    if (error) throw error;
    
    const totalJoined = data.length;
    const totalEarned = data.reduce((sum, referral) => {
      const earnings = referral.referral_earnings.reduce((earnSum, earning) => 
        earnSum + parseFloat(earning.commission_amount), 0);
      return sum + earnings;
    }, 0);

    return {
      totalJoined,
      referralEarn: totalEarned,
      referrals: data
    };
  },

  // Daily earnings functions
  async getDailyEarnings(userId, startDate, endDate) {
    let query = supabase
      .from('daily_earnings')
      .select('*')
      .eq('user_id', userId)
      .order('earning_date', { ascending: false });

    if (startDate) {
      query = query.gte('earning_date', startDate);
    }
    if (endDate) {
      query = query.lte('earning_date', endDate);
    }

    const { data, error } = await query;
    
    if (error) throw error;
    return data;
  },

  // KYC functions
  async getKYCStatus(userId) {
    const { data, error } = await supabase
      .from('users')
      .select('kyc_status')
      .eq('id', userId)
      .single();
    
    if (error) throw error;
    return data.kyc_status;
  },

  async getKYCDocuments(userId) {
    const { data, error } = await supabase
      .from('kyc_documents')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  },

  async uploadKYCDocument(userId, documentType, file) {
    // Upload file to Supabase Storage
    const fileName = `${userId}/${documentType}_${Date.now()}.${file.name.split('.').pop()}`;
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('kyc-documents')
      .upload(fileName, file);

    if (uploadError) throw uploadError;

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('kyc-documents')
      .getPublicUrl(fileName);

    // Save document record
    const { data, error } = await supabase
      .from('kyc_documents')
      .insert({
        user_id: userId,
        document_type: documentType,
        document_url: publicUrl
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }
};

// Real-time subscriptions
export const subscribeToTransactions = (userId, callback) => {
  return supabase
    .channel('transactions')
    .on('postgres_changes', 
      { 
        event: '*', 
        schema: 'public', 
        table: 'transactions',
        filter: `user_id=eq.${userId}`
      }, 
      callback
    )
    .subscribe();
};

export const subscribeToInvestments = (userId, callback) => {
  return supabase
    .channel('investments')
    .on('postgres_changes', 
      { 
        event: '*', 
        schema: 'public', 
        table: 'investments',
        filter: `user_id=eq.${userId}`
      }, 
      callback
    )
    .subscribe();
};
