import axios from 'axios';

class BitcoinService {
  constructor() {
    this.apiBase = 'https://api.blockcypher.com/v1/btc/main';
    this.address = '******************************************';
    this.cache = new Map();
    this.cacheTimeout = 30000; // 30 seconds cache
  }

  // Get Bitcoin price in USD
  async getBitcoinPrice() {
    const cacheKey = 'btc_price';
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      // Using CoinGecko for price (free API)
      const response = await axios.get('https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd');
      const price = response.data.bitcoin.usd;
      
      this.setCachedData(cacheKey, price);
      return price;
    } catch (error) {
      console.error('Error fetching Bitcoin price:', error);
      // Fallback price if API fails
      return 45000;
    }
  }

  // Convert USD to Bitcoin
  async convertUsdToBitcoin(usdAmount) {
    try {
      const btcPrice = await this.getBitcoinPrice();
      const btcAmount = usdAmount / btcPrice;
      
      return {
        btcAmount: btcAmount,
        btcPrice: btcPrice,
        formattedAmount: btcAmount.toFixed(8)
      };
    } catch (error) {
      console.error('Error converting USD to Bitcoin:', error);
      throw error;
    }
  }

  // Get address balance and transactions
  async getAddressInfo() {
    const cacheKey = `address_${this.address}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const response = await axios.get(`${this.apiBase}/addrs/${this.address}/balance`);
      const data = {
        balance: response.data.balance / 100000000, // Convert satoshis to BTC
        unconfirmed_balance: response.data.unconfirmed_balance / 100000000,
        total_received: response.data.total_received / 100000000,
        total_sent: response.data.total_sent / 100000000,
        n_tx: response.data.n_tx,
        unconfirmed_n_tx: response.data.unconfirmed_n_tx
      };
      
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Error fetching address info:', error);
      throw error;
    }
  }

  // Get recent transactions for the address
  async getRecentTransactions(limit = 50) {
    try {
      const response = await axios.get(`${this.apiBase}/addrs/${this.address}/full?limit=${limit}`);
      
      if (!response.data.txs) {
        return [];
      }

      return response.data.txs.map(tx => ({
        hash: tx.hash,
        block_height: tx.block_height,
        block_index: tx.block_index,
        received: new Date(tx.received),
        confirmed: new Date(tx.confirmed),
        confirmations: tx.confirmations,
        inputs: tx.inputs.map(input => ({
          addresses: input.addresses,
          output_value: input.output_value / 100000000 // Convert to BTC
        })),
        outputs: tx.outputs.map(output => ({
          addresses: output.addresses,
          value: output.value / 100000000 // Convert to BTC
        })),
        total: tx.total / 100000000, // Convert to BTC
        fees: tx.fees / 100000000, // Convert to BTC
        size: tx.size,
        preference: tx.preference,
        relayed_by: tx.relayed_by,
        confidence: tx.confidence
      }));
    } catch (error) {
      console.error('Error fetching transactions:', error);
      return [];
    }
  }

  // Get transactions received to our address (incoming payments)
  async getIncomingTransactions(limit = 50) {
    try {
      const transactions = await this.getRecentTransactions(limit);
      
      // Filter for transactions that sent money TO our address
      const incomingTxs = transactions.filter(tx => {
        return tx.outputs.some(output => 
          output.addresses && output.addresses.includes(this.address)
        );
      });

      // Calculate the amount received in each transaction
      return incomingTxs.map(tx => {
        const receivedAmount = tx.outputs
          .filter(output => output.addresses && output.addresses.includes(this.address))
          .reduce((sum, output) => sum + output.value, 0);

        return {
          ...tx,
          received_amount: receivedAmount,
          received_amount_usd: null // Will be calculated when needed
        };
      });
    } catch (error) {
      console.error('Error fetching incoming transactions:', error);
      return [];
    }
  }

  // Check for new transactions since a specific time
  async getTransactionsSince(sinceDate) {
    try {
      const transactions = await this.getIncomingTransactions();
      
      return transactions.filter(tx => {
        const txDate = new Date(tx.confirmed || tx.received);
        return txDate > sinceDate;
      });
    } catch (error) {
      console.error('Error fetching transactions since date:', error);
      return [];
    }
  }

  // Get specific transaction details
  async getTransaction(txHash) {
    const cacheKey = `tx_${txHash}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const response = await axios.get(`${this.apiBase}/txs/${txHash}`);
      const tx = response.data;
      
      const txData = {
        hash: tx.hash,
        block_height: tx.block_height,
        block_index: tx.block_index,
        received: new Date(tx.received),
        confirmed: tx.confirmed ? new Date(tx.confirmed) : null,
        confirmations: tx.confirmations,
        total: tx.total / 100000000,
        fees: tx.fees / 100000000,
        size: tx.size,
        confidence: tx.confidence,
        inputs: tx.inputs.map(input => ({
          addresses: input.addresses,
          output_value: input.output_value / 100000000
        })),
        outputs: tx.outputs.map(output => ({
          addresses: output.addresses,
          value: output.value / 100000000
        }))
      };
      
      this.setCachedData(cacheKey, txData);
      return txData;
    } catch (error) {
      console.error('Error fetching transaction:', error);
      throw error;
    }
  }

  // Calculate USD value of Bitcoin amount at time of transaction
  async calculateUsdValue(btcAmount, txDate) {
    try {
      // For real-time transactions, use current price
      const now = new Date();
      const txTime = new Date(txDate);
      const timeDiff = now - txTime;
      
      // If transaction is less than 1 hour old, use current price
      if (timeDiff < 3600000) {
        const currentPrice = await this.getBitcoinPrice();
        return btcAmount * currentPrice;
      }
      
      // For older transactions, you might want to use historical price APIs
      // For now, we'll use current price as approximation
      const currentPrice = await this.getBitcoinPrice();
      return btcAmount * currentPrice;
    } catch (error) {
      console.error('Error calculating USD value:', error);
      return 0;
    }
  }

  // Cache management
  getCachedData(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  setCachedData(key, data) {
    this.cache.set(key, {
      data: data,
      timestamp: Date.now()
    });
  }

  clearCache() {
    this.cache.clear();
  }

  // Get network fee estimates
  async getNetworkFees() {
    try {
      const response = await axios.get(`${this.apiBase}`);
      return {
        high_fee_per_kb: response.data.high_fee_per_kb,
        medium_fee_per_kb: response.data.medium_fee_per_kb,
        low_fee_per_kb: response.data.low_fee_per_kb
      };
    } catch (error) {
      console.error('Error fetching network fees:', error);
      return {
        high_fee_per_kb: 50000,
        medium_fee_per_kb: 25000,
        low_fee_per_kb: 10000
      };
    }
  }

  // Validate Bitcoin address format
  isValidBitcoinAddress(address) {
    // Basic validation for Bitcoin addresses
    const bech32Regex = /^bc1[a-z0-9]{39,59}$/;
    const legacyRegex = /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/;
    const segwitRegex = /^3[a-km-zA-HJ-NP-Z1-9]{25,34}$/;
    
    return bech32Regex.test(address) || legacyRegex.test(address) || segwitRegex.test(address);
  }

  // Get address type
  getAddressType(address) {
    if (address.startsWith('bc1')) return 'bech32';
    if (address.startsWith('3')) return 'segwit';
    if (address.startsWith('1')) return 'legacy';
    return 'unknown';
  }
}

export default new BitcoinService();
