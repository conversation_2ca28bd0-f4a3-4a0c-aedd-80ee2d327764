/* Admin Dashboard Styles */
.admin-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  padding: 20px;
}

.admin-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
}

.admin-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #00d4aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-header-left h1 {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 4px 0;
}

.admin-header-left p {
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

.admin-header-right {
  display: flex;
  gap: 12px;
}

.admin-refresh-btn, .admin-logout-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.admin-refresh-btn {
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  color: #00d4aa;
}

.admin-refresh-btn:hover {
  background: rgba(0, 212, 170, 0.2);
  border-color: #00d4aa;
}

.admin-logout-btn {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  color: #dc3545;
}

.admin-logout-btn:hover {
  background: rgba(220, 53, 69, 0.2);
  border-color: #dc3545;
}

.admin-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.admin-stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 20px;
}

.admin-stat-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  font-weight: 700;
  flex-shrink: 0;
}

.admin-stat-icon.bitcoin {
  background: linear-gradient(135deg, #f7931a, #e6820e);
}

.admin-stat-icon.deposits {
  background: linear-gradient(135deg, #ffc107, #e0a800);
}

.admin-stat-icon.confirmed {
  background: linear-gradient(135deg, #28a745, #1e7e34);
}

.admin-stat-info h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: rgba(255, 255, 255, 0.8);
}

.bitcoin-address {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #00d4aa;
  word-break: break-all;
  margin-bottom: 8px;
}

.bitcoin-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.bitcoin-stats span {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #00d4aa;
  margin: 0 0 4px 0;
}

.admin-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.section-header {
  margin-bottom: 20px;
}

.section-header h2 {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 4px 0;
}

.section-header p {
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
}

.deposits-table-container {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.deposits-table {
  width: 100%;
  border-collapse: collapse;
}

.deposits-table th {
  background: rgba(255, 255, 255, 0.05);
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.deposits-table td {
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.deposit-row:hover {
  background: rgba(255, 255, 255, 0.02);
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-info strong {
  color: white;
  font-size: 14px;
}

.user-info small {
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
}

.amount-usd {
  font-weight: 600;
  color: #00d4aa;
  font-size: 16px;
}

.amount-btc {
  font-family: 'Courier New', monospace;
  color: #f7931a;
  font-size: 14px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.date {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.deposit-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s ease;
}

.action-btn.confirm {
  background: rgba(40, 167, 69, 0.1);
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.action-btn.confirm:hover {
  background: rgba(40, 167, 69, 0.2);
  transform: scale(1.1);
}

.action-btn.reject {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.action-btn.reject:hover {
  background: rgba(220, 53, 69, 0.2);
  transform: scale(1.1);
}

.action-btn.details {
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
}

.action-btn.details:hover {
  background: rgba(0, 212, 170, 0.2);
  transform: scale(1.1);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.no-deposits {
  padding: 40px;
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 24px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.modal-close:hover {
  color: white;
}

.modal-body {
  padding: 24px;
}

.deposit-detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

.detail-item span {
  color: white;
  font-size: 14px;
}

.tx-hash {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  word-break: break-all;
  color: #00d4aa;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .admin-dashboard {
    padding: 16px;
  }
  
  .admin-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .admin-header-right {
    width: 100%;
    justify-content: flex-end;
  }
  
  .admin-stats {
    grid-template-columns: 1fr;
  }
  
  .admin-stat-card {
    padding: 16px;
  }
  
  .admin-stat-icon {
    width: 48px;
    height: 48px;
    font-size: 24px;
  }
  
  .deposits-table-container {
    overflow-x: auto;
  }
  
  .deposits-table {
    min-width: 800px;
  }
  
  .deposits-table th,
  .deposits-table td {
    padding: 12px 8px;
  }
  
  .deposit-detail-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-content {
    margin: 20px;
    max-height: calc(100vh - 40px);
  }
}
