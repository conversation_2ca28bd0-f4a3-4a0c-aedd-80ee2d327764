import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import adminAuthService from '../../services/adminAuthService.js';
import bitcoinService from '../../services/bitcoinService.js';
import { supabase } from '../../lib/supabase.js';
import '../styles/AdminDashboard.css';

const AdminDashboard = () => {
  const [admin, setAdmin] = useState(null);
  const [loading, setLoading] = useState(true);
  const [deposits, setDeposits] = useState([]);
  const [bitcoinInfo, setBitcoinInfo] = useState(null);
  const [recentTransactions, setRecentTransactions] = useState([]);
  const [selectedDeposit, setSelectedDeposit] = useState(null);
  const [confirmingDeposit, setConfirmingDeposit] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    checkAuth();
    loadDashboardData();
    
    // Set up real-time subscription for deposits
    const subscription = supabase
      .channel('admin_deposits')
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'crypto_deposits'
        }, 
        () => {
          loadDeposits(); // Reload deposits when changes occur
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const checkAuth = async () => {
    const isAuth = await adminAuthService.isAuthenticated();
    if (!isAuth) {
      navigate('/app/admin/login');
      return;
    }

    const currentAdmin = await adminAuthService.getCurrentAdmin();
    setAdmin(currentAdmin);
  };

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadDeposits(),
        loadBitcoinInfo(),
        loadRecentTransactions()
      ]);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadDeposits = async () => {
    try {
      const { data, error } = await supabase
        .from('crypto_deposits')
        .select(`
          *,
          users (
            name,
            email
          )
        `)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;
      setDeposits(data || []);
    } catch (error) {
      console.error('Error loading deposits:', error);
    }
  };

  const loadBitcoinInfo = async () => {
    try {
      const info = await bitcoinService.getAddressInfo();
      setBitcoinInfo(info);
    } catch (error) {
      console.error('Error loading Bitcoin info:', error);
    }
  };

  const loadRecentTransactions = async () => {
    try {
      const transactions = await bitcoinService.getIncomingTransactions(10);
      setRecentTransactions(transactions);
    } catch (error) {
      console.error('Error loading recent transactions:', error);
    }
  };

  const handleConfirmDeposit = async (deposit) => {
    if (!window.confirm(`Confirm deposit of $${deposit.amount_usd} for ${deposit.users?.name || 'Unknown User'}?`)) {
      return;
    }

    setConfirmingDeposit(deposit.id);
    
    try {
      // Update deposit status to confirmed
      const { error: depositError } = await supabase
        .from('crypto_deposits')
        .update({
          status: 'confirmed',
          confirmed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', deposit.id);

      if (depositError) throw depositError;

      // Update user balance
      const { error: balanceError } = await supabase
        .from('users')
        .update({
          balance: supabase.raw(`COALESCE(balance, 0) + ${deposit.amount_usd}`),
          updated_at: new Date().toISOString()
        })
        .eq('id', deposit.user_id);

      if (balanceError) throw balanceError;

      // Create transaction record
      const { error: transactionError } = await supabase
        .from('transactions')
        .insert({
          user_id: deposit.user_id,
          type: 'deposit',
          amount: deposit.amount_usd,
          status: 'completed',
          description: `Bitcoin deposit - ${deposit.crypto_amount} BTC`,
          created_at: new Date().toISOString()
        });

      if (transactionError) throw transactionError;

      alert('Deposit confirmed successfully!');
      loadDeposits(); // Reload deposits

    } catch (error) {
      console.error('Error confirming deposit:', error);
      alert('Error confirming deposit: ' + error.message);
    } finally {
      setConfirmingDeposit(null);
    }
  };

  const handleRejectDeposit = async (deposit) => {
    if (!window.confirm(`Reject deposit of $${deposit.amount_usd} for ${deposit.users?.name || 'Unknown User'}?`)) {
      return;
    }

    try {
      const { error } = await supabase
        .from('crypto_deposits')
        .update({
          status: 'failed',
          updated_at: new Date().toISOString()
        })
        .eq('id', deposit.id);

      if (error) throw error;

      alert('Deposit rejected.');
      loadDeposits();

    } catch (error) {
      console.error('Error rejecting deposit:', error);
      alert('Error rejecting deposit: ' + error.message);
    }
  };

  const handleLogout = async () => {
    await adminAuthService.logout();
    navigate('/app/admin/login');
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { color: '#ffc107', bg: 'rgba(255, 193, 7, 0.1)', text: 'Pending' },
      detected: { color: '#007bff', bg: 'rgba(0, 123, 255, 0.1)', text: 'Detected' },
      confirmed: { color: '#28a745', bg: 'rgba(40, 167, 69, 0.1)', text: 'Confirmed' },
      failed: { color: '#dc3545', bg: 'rgba(220, 53, 69, 0.1)', text: 'Failed' },
      expired: { color: '#6c757d', bg: 'rgba(108, 117, 125, 0.1)', text: 'Expired' }
    };

    const config = statusConfig[status] || statusConfig.pending;

    return (
      <span 
        className="status-badge"
        style={{ 
          color: config.color, 
          backgroundColor: config.bg,
          border: `1px solid ${config.color}30`
        }}
      >
        {config.text}
      </span>
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const formatBTC = (amount) => {
    return parseFloat(amount).toFixed(8);
  };

  if (loading) {
    return (
      <div className="admin-loading">
        <div className="admin-loading-spinner"></div>
        <p>Loading admin dashboard...</p>
      </div>
    );
  }

  return (
    <div className="admin-dashboard">
      <div className="admin-header">
        <div className="admin-header-left">
          <h1>🔐 BullSeed Admin Dashboard</h1>
          <p>Welcome back, {admin?.username}</p>
        </div>
        <div className="admin-header-right">
          <button onClick={loadDashboardData} className="admin-refresh-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="23,4 23,10 17,10"/>
              <polyline points="1,20 1,14 7,14"/>
              <path d="M20.49,9A9,9,0,0,0,5.64,5.64L1,10m22,4L18.36,18.36A9,9,0,0,1,3.51,15"/>
            </svg>
            Refresh
          </button>
          <button onClick={handleLogout} className="admin-logout-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
              <polyline points="16,17 21,12 16,7"/>
              <line x1="21" y1="12" x2="9" y2="12"/>
            </svg>
            Logout
          </button>
        </div>
      </div>

      <div className="admin-stats">
        <div className="admin-stat-card">
          <div className="admin-stat-icon bitcoin">₿</div>
          <div className="admin-stat-info">
            <h3>Bitcoin Address</h3>
            <p className="bitcoin-address">******************************************</p>
            {bitcoinInfo && (
              <div className="bitcoin-stats">
                <span>Balance: {formatBTC(bitcoinInfo.balance)} BTC</span>
                <span>Total Received: {formatBTC(bitcoinInfo.total_received)} BTC</span>
                <span>Transactions: {bitcoinInfo.n_tx}</span>
              </div>
            )}
          </div>
        </div>

        <div className="admin-stat-card">
          <div className="admin-stat-icon deposits">📥</div>
          <div className="admin-stat-info">
            <h3>Pending Deposits</h3>
            <p className="stat-number">{deposits.filter(d => d.status === 'pending').length}</p>
            <span>Awaiting confirmation</span>
          </div>
        </div>

        <div className="admin-stat-card">
          <div className="admin-stat-icon confirmed">✅</div>
          <div className="admin-stat-info">
            <h3>Confirmed Today</h3>
            <p className="stat-number">
              {deposits.filter(d => 
                d.status === 'confirmed' && 
                new Date(d.confirmed_at).toDateString() === new Date().toDateString()
              ).length}
            </p>
            <span>Processed deposits</span>
          </div>
        </div>
      </div>

      <div className="admin-content">
        <div className="admin-deposits-section">
          <div className="section-header">
            <h2>💰 Deposit Management</h2>
            <p>Review and confirm user deposits</p>
          </div>

          <div className="deposits-table-container">
            <table className="deposits-table">
              <thead>
                <tr>
                  <th>User</th>
                  <th>Amount</th>
                  <th>Bitcoin Amount</th>
                  <th>Status</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {deposits.map(deposit => (
                  <tr key={deposit.id} className={`deposit-row ${deposit.status}`}>
                    <td>
                      <div className="user-info">
                        <strong>{deposit.users?.name || 'Unknown'}</strong>
                        <small>{deposit.users?.email || 'No email'}</small>
                      </div>
                    </td>
                    <td>
                      <span className="amount-usd">${deposit.amount_usd}</span>
                    </td>
                    <td>
                      <span className="amount-btc">{formatBTC(deposit.crypto_amount)} BTC</span>
                    </td>
                    <td>
                      {getStatusBadge(deposit.status)}
                    </td>
                    <td>
                      <span className="date">{formatDate(deposit.created_at)}</span>
                    </td>
                    <td>
                      <div className="deposit-actions">
                        {deposit.status === 'pending' && (
                          <>
                            <button
                              onClick={() => handleConfirmDeposit(deposit)}
                              className="action-btn confirm"
                              disabled={confirmingDeposit === deposit.id}
                            >
                              {confirmingDeposit === deposit.id ? '...' : '✅'}
                            </button>
                            <button
                              onClick={() => handleRejectDeposit(deposit)}
                              className="action-btn reject"
                            >
                              ❌
                            </button>
                          </>
                        )}
                        <button
                          onClick={() => setSelectedDeposit(deposit)}
                          className="action-btn details"
                        >
                          👁️
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {deposits.length === 0 && (
              <div className="no-deposits">
                <p>No deposits found</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Deposit Details Modal */}
      {selectedDeposit && (
        <div className="modal-overlay" onClick={() => setSelectedDeposit(null)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>Deposit Details</h3>
              <button onClick={() => setSelectedDeposit(null)} className="modal-close">×</button>
            </div>
            <div className="modal-body">
              <div className="deposit-detail-grid">
                <div className="detail-item">
                  <label>User:</label>
                  <span>{selectedDeposit.users?.name || 'Unknown'}</span>
                </div>
                <div className="detail-item">
                  <label>Email:</label>
                  <span>{selectedDeposit.users?.email || 'No email'}</span>
                </div>
                <div className="detail-item">
                  <label>USD Amount:</label>
                  <span>${selectedDeposit.amount_usd}</span>
                </div>
                <div className="detail-item">
                  <label>Bitcoin Amount:</label>
                  <span>{formatBTC(selectedDeposit.crypto_amount)} BTC</span>
                </div>
                <div className="detail-item">
                  <label>Status:</label>
                  {getStatusBadge(selectedDeposit.status)}
                </div>
                <div className="detail-item">
                  <label>Created:</label>
                  <span>{formatDate(selectedDeposit.created_at)}</span>
                </div>
                <div className="detail-item">
                  <label>Deposit Address:</label>
                  <span className="bitcoin-address">{selectedDeposit.deposit_address}</span>
                </div>
                {selectedDeposit.transaction_hash && (
                  <div className="detail-item">
                    <label>Transaction Hash:</label>
                    <span className="tx-hash">{selectedDeposit.transaction_hash}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;
