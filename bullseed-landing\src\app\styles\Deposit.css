/* Deposit Page Styles */
.deposit {
  padding: 32px;
  max-width: 1200px;
  margin: 0 auto;
  min-height: calc(100vh - 120px);
}

.deposit-header {
  margin-bottom: 32px;
  text-align: center;
}

.deposit-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
}

.deposit-header p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
}

.deposit-content {
  display: grid;
  grid-template-columns: 1fr 320px;
  gap: 32px;
  align-items: start;
}

.deposit-main {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 32px;
  backdrop-filter: blur(10px);
}

.deposit-sidebar {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Step Styles */
.deposit-step {
  width: 100%;
}

.deposit-step-header {
  margin-bottom: 32px;
  text-align: center;
}

.deposit-step-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: white;
  margin-bottom: 8px;
}

.deposit-step-header p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

.deposit-back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  cursor: pointer;
  margin-bottom: 16px;
  padding: 8px 0;
  transition: color 0.2s ease;
}

.deposit-back-btn:hover {
  color: #00d4aa;
}

/* Amount Step */
.deposit-amount-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  margin-bottom: 32px;
}

.deposit-amount-input-container {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0 20px;
  transition: border-color 0.2s ease;
}

.deposit-amount-input-container:focus-within {
  border-color: #00d4aa;
}

.deposit-currency-symbol {
  font-size: 24px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.7);
  margin-right: 8px;
}

.deposit-amount-input {
  background: none;
  border: none;
  color: white;
  font-size: 32px;
  font-weight: 600;
  padding: 20px 0;
  width: 200px;
  text-align: center;
  outline: none;
}

.deposit-amount-input::placeholder {
  color: rgba(255, 255, 255, 0.3);
}

.deposit-currency-label {
  font-size: 16px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  margin-left: 8px;
}

.deposit-continue-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
  border: none;
  color: white;
  font-size: 16px;
  font-weight: 600;
  padding: 16px 32px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.deposit-continue-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.deposit-quick-amounts {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.deposit-quick-amounts span {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

.deposit-quick-amount-btn {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.deposit-quick-amount-btn:hover {
  background: rgba(0, 212, 170, 0.1);
  border-color: #00d4aa;
  color: #00d4aa;
}

/* Crypto Selection Step */
.deposit-crypto-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.deposit-crypto-option {
  display: flex;
  align-items: center;
  gap: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.deposit-crypto-option:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: #00d4aa;
  transform: translateY(-2px);
}

.deposit-crypto-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
  color: white;
  flex-shrink: 0;
}

.deposit-crypto-info {
  flex: 1;
}

.deposit-crypto-name {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.deposit-crypto-symbol {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 2px;
}

.deposit-crypto-network {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.deposit-crypto-arrow {
  color: rgba(255, 255, 255, 0.4);
  transition: color 0.2s ease;
}

.deposit-crypto-option:hover .deposit-crypto-arrow {
  color: #00d4aa;
}

/* Payment Step */
.deposit-payment-container {
  display: grid;
  grid-template-columns: 1fr 280px;
  gap: 32px;
}

.deposit-payment-main {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.deposit-payment-summary {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 20px;
}

.deposit-payment-crypto {
  display: flex;
  align-items: center;
  gap: 16px;
}

.deposit-payment-crypto-icon {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  font-weight: 700;
  color: white;
  flex-shrink: 0;
}

.deposit-payment-crypto-info {
  flex: 1;
}

.deposit-payment-crypto-amount {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.deposit-payment-crypto-usd {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2px;
}

.deposit-payment-crypto-rate {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.deposit-payment-address {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 20px;
}

.deposit-payment-address h3 {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 16px;
}

.deposit-address-input-wrapper {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.deposit-address-input {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-family: 'Courier New', monospace;
  word-break: break-all;
}

.deposit-copy-btn {
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  color: #00d4aa;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.deposit-copy-btn:hover {
  background: rgba(0, 212, 170, 0.2);
  border-color: #00d4aa;
}

.deposit-network-info {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.deposit-payment-warnings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.deposit-warning {
  display: flex;
  gap: 12px;
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.deposit-warning svg {
  color: #ffc107;
  flex-shrink: 0;
  margin-top: 2px;
}

.deposit-payment-status {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 20px;
}

.deposit-status-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.deposit-status-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.deposit-status-value {
  font-size: 14px;
  font-weight: 600;
  color: white;
}

.deposit-status-value.pending {
  color: #ffc107;
}

.deposit-status-value.detected {
  color: #007bff;
}

.deposit-status-value.confirmed {
  color: #28a745;
}

.deposit-status-value.failed,
.deposit-status-value.expired {
  color: #dc3545;
}

.transaction-hash {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.deposit-copy-hash-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.deposit-copy-hash-btn:hover {
  color: #00d4aa;
}

/* Status Notifications */
.deposit-status-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(0, 212, 170, 0.95);
  border: 1px solid #00d4aa;
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 212, 170, 0.3);
  z-index: 1000;
  animation: slideInRight 0.3s ease-out;
  max-width: 300px;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.deposit-status-notification-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  color: white;
}

.deposit-status-notification-content svg {
  color: white;
  flex-shrink: 0;
  margin-top: 2px;
}

.deposit-status-notification-content strong {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
  display: block;
}

.deposit-status-notification-content p {
  font-size: 13px;
  margin: 0;
  opacity: 0.9;
}

/* Payment Sidebar */
.deposit-payment-sidebar {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.deposit-qr-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
}

.deposit-qr-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 16px;
}

.deposit-qr-container {
  display: flex;
  justify-content: center;
  margin-bottom: 12px;
}

.deposit-qr-code {
  width: 200px;
  height: 200px;
  border-radius: 8px;
  background: white;
  padding: 8px;
}

.deposit-qr-placeholder {
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.4);
}

.deposit-qr-instruction {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.deposit-instructions {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
}

.deposit-instructions h3 {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 16px;
}

.deposit-instructions ol {
  list-style: none;
  counter-reset: step-counter;
  padding: 0;
  margin: 0;
}

.deposit-instructions li {
  counter-increment: step-counter;
  position: relative;
  padding-left: 32px;
  margin-bottom: 12px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}

.deposit-instructions li::before {
  content: counter(step-counter);
  position: absolute;
  left: 0;
  top: 0;
  width: 20px;
  height: 20px;
  background: #00d4aa;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

/* Deposit History */
.deposit-history-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
}

.deposit-history-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 16px;
}

.deposit-history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.deposit-history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.deposit-history-crypto {
  display: flex;
  align-items: center;
  gap: 12px;
}

.deposit-history-crypto-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 700;
  color: white;
  flex-shrink: 0;
}

.deposit-history-crypto-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.deposit-history-amount {
  font-size: 14px;
  font-weight: 600;
  color: white;
}

.deposit-history-usd {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.deposit-history-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.deposit-history-status-badge {
  font-size: 11px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.deposit-history-status-badge.pending {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
}

.deposit-history-status-badge.detected {
  background: rgba(0, 123, 255, 0.2);
  color: #007bff;
}

.deposit-history-status-badge.confirmed {
  background: rgba(40, 167, 69, 0.2);
  color: #28a745;
}

.deposit-history-status-badge.failed,
.deposit-history-status-badge.expired {
  background: rgba(220, 53, 69, 0.2);
  color: #dc3545;
}

.deposit-history-date {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.4);
}

.crypto-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.crypto-option {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
}

.crypto-option:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.crypto-option.active {
  background: rgba(0, 212, 170, 0.1);
  border-color: #00d4aa;
}

.crypto-option-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 700;
}

.crypto-option-info {
  flex: 1;
}

.crypto-option-name {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.crypto-option-symbol {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

.crypto-option-network {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

/* Support Section */
.deposit-support {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
}

.deposit-support h3 {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 12px;
}

.deposit-support p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 16px;
  line-height: 1.4;
}

.deposit-support-btn {
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  color: #00d4aa;
  font-size: 14px;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
}

.deposit-support-btn:hover {
  background: rgba(0, 212, 170, 0.15);
  border-color: rgba(0, 212, 170, 0.5);
}

/* Loading States */
.deposit-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.deposit-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #00d4aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.deposit-loading p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
}

/* Error States */
.deposit-error {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  color: #dc3545;
  font-size: 14px;
  margin-top: 16px;
}

.deposit-retry-btn {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  color: #dc3545;
  font-size: 14px;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 12px;
}

.deposit-retry-btn:hover {
  background: rgba(220, 53, 69, 0.15);
  border-color: rgba(220, 53, 69, 0.5);
}

.deposit-copy-btn {
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  color: #00d4aa;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.deposit-copy-btn:hover {
  background: rgba(0, 212, 170, 0.15);
  border-color: rgba(0, 212, 170, 0.5);
}

.deposit-qr-code {
  text-align: center;
}

.qr-code-image {
  width: 200px;
  height: 200px;
  border-radius: 8px;
  margin-bottom: 12px;
}

.deposit-qr-code p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

.deposit-info {
  margin-bottom: 24px;
}

.deposit-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.deposit-info-item:last-child {
  border-bottom: none;
}

.deposit-info-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.deposit-info-value {
  font-size: 14px;
  color: white;
  font-weight: 600;
}

.deposit-warnings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.deposit-warning {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
  border-radius: 8px;
  color: #f59e0b;
  font-size: 14px;
  line-height: 1.5;
}

.deposit-warning svg {
  flex-shrink: 0;
  margin-top: 2px;
}

.deposit-sidebar {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.deposit-history {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(10px);
}

.deposit-history h3 {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin-bottom: 20px;
}

.deposit-history-empty {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.6);
}

.deposit-history-empty svg {
  margin-bottom: 16px;
  opacity: 0.5;
}

.deposit-history-empty p {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.deposit-history-empty span {
  font-size: 14px;
  opacity: 0.8;
}

.deposit-support {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(10px);
}

.deposit-support h3 {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin-bottom: 12px;
}

.deposit-support p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 20px;
  line-height: 1.5;
}

.deposit-support-btn {
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  color: #00d4aa;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  justify-content: center;
}

.deposit-support-btn:hover {
  background: rgba(0, 212, 170, 0.15);
  border-color: rgba(0, 212, 170, 0.5);
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .deposit-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .deposit-payment-container {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .deposit-crypto-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .deposit {
    padding: 16px;
  }

  .deposit-header {
    margin-bottom: 24px;
  }

  .deposit-header h1 {
    font-size: 24px;
  }

  .deposit-main {
    padding: 20px;
  }

  .deposit-amount-input-container {
    padding: 0 16px;
  }

  .deposit-amount-input {
    font-size: 24px;
    width: 150px;
  }

  .deposit-crypto-option {
    padding: 16px;
  }

  .deposit-crypto-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .deposit-payment-crypto-icon {
    width: 48px;
    height: 48px;
    font-size: 24px;
  }

  .deposit-payment-crypto-amount {
    font-size: 18px;
  }

  .deposit-qr-code {
    width: 160px;
    height: 160px;
  }

  .deposit-qr-placeholder {
    width: 160px;
    height: 160px;
  }

  .deposit-payment-status {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .deposit-quick-amounts {
    justify-content: center;
  }

  .deposit-step-header {
    margin-bottom: 24px;
  }

  .deposit-step-header h2 {
    font-size: 20px;
  }

  .qr-code-image {
    width: 150px;
    height: 150px;
  }
}

@media (max-width: 480px) {
  .deposit {
    padding: 12px;
  }

  .deposit-main {
    padding: 16px;
  }

  .deposit-amount-input {
    font-size: 20px;
    width: 120px;
  }

  .deposit-continue-btn {
    padding: 14px 24px;
    font-size: 14px;
  }

  .deposit-crypto-option {
    padding: 12px;
  }

  .deposit-crypto-icon {
    width: 36px;
    height: 36px;
    font-size: 18px;
  }

  .deposit-crypto-name {
    font-size: 14px;
  }

  .deposit-crypto-symbol {
    font-size: 12px;
  }

  .deposit-crypto-network {
    font-size: 11px;
  }

  .deposit-payment-container {
    gap: 20px;
  }

  .deposit-payment-main {
    gap: 20px;
  }

  .deposit-payment-summary,
  .deposit-payment-address,
  .deposit-payment-status {
    padding: 16px;
  }

  .deposit-qr-code,
  .deposit-qr-placeholder {
    width: 140px;
    height: 140px;
  }

  .deposit-history-section,
  .deposit-support,
  .deposit-qr-section,
  .deposit-instructions {
    padding: 16px;
  }

  .qr-code-image {
    width: 120px;
    height: 120px;
  }
}
