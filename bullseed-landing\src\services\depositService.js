import { supabase } from '../lib/supabase.js';
import cryptoService from './cryptoService.js';
import QRCode from 'qrcode';

class DepositService {
  // Create a new deposit request
  async createDeposit(userId, usdAmount, cryptoId) {
    try {
      // Validate inputs
      if (!userId || !usdAmount || !cryptoId) {
        throw new Error('Missing required parameters');
      }

      if (usdAmount < 200) {
        throw new Error('Minimum deposit amount is $200');
      }

      // Get crypto details
      const crypto = cryptoService.getSupportedCryptocurrencies().find(c => c.id === cryptoId);
      if (!crypto) {
        throw new Error('Unsupported cryptocurrency');
      }

      // Convert USD to crypto amount
      const conversion = await cryptoService.convertUsdToCrypto(usdAmount, cryptoId);
      
      // Check minimum deposit in crypto
      if (conversion.cryptoAmount < crypto.minDeposit) {
        throw new Error(`Minimum deposit is ${crypto.minDeposit} ${crypto.symbol}`);
      }

      // Generate unique deposit address
      const depositAddress = cryptoService.generateDepositAddress(cryptoId, userId);
      
      // Generate QR code data
      const qrData = await this.generateQRCode(depositAddress, conversion.cryptoAmount, crypto.symbol);

      // Create deposit record in database
      const { data: deposit, error } = await supabase
        .from('crypto_deposits')
        .insert({
          user_id: userId,
          amount_usd: usdAmount,
          cryptocurrency: crypto.symbol,
          crypto_amount: conversion.cryptoAmount,
          deposit_address: depositAddress,
          qr_code_data: qrData,
          required_confirmations: crypto.confirmations,
          status: 'pending'
        })
        .select()
        .single();

      if (error) {
        console.error('Database error creating deposit:', error);
        throw new Error('Failed to create deposit request');
      }

      return {
        ...deposit,
        crypto_details: crypto,
        conversion_rate: conversion.cryptoPrice,
        formatted_amount: conversion.formattedAmount
      };

    } catch (error) {
      console.error('Error creating deposit:', error);
      throw error;
    }
  }

  // Generate QR code for crypto payment
  async generateQRCode(address, amount, symbol) {
    try {
      let qrString;
      
      // Create appropriate QR code format for each cryptocurrency
      switch (symbol) {
        case 'BTC':
          qrString = `bitcoin:${address}?amount=${amount}`;
          break;
        case 'ETH':
          qrString = `ethereum:${address}?value=${amount}`;
          break;
        case 'USDT':
        case 'USDC':
          qrString = `ethereum:${address}?value=${amount}`;
          break;
        default:
          qrString = address;
      }

      // Generate QR code as data URL
      const qrCodeDataURL = await QRCode.toDataURL(qrString, {
        width: 256,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });

      return qrCodeDataURL;
    } catch (error) {
      console.error('Error generating QR code:', error);
      return null;
    }
  }

  // Get user's deposit history
  async getUserDeposits(userId, limit = 20) {
    try {
      const { data: deposits, error } = await supabase
        .from('crypto_deposits')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching deposits:', error);
        return [];
      }

      return deposits || [];
    } catch (error) {
      console.error('Error in getUserDeposits:', error);
      return [];
    }
  }

  // Get deposit by ID
  async getDeposit(depositId) {
    try {
      const { data: deposit, error } = await supabase
        .from('crypto_deposits')
        .select('*')
        .eq('id', depositId)
        .single();

      if (error) {
        console.error('Error fetching deposit:', error);
        return null;
      }

      return deposit;
    } catch (error) {
      console.error('Error in getDeposit:', error);
      return null;
    }
  }

  // Update deposit status (for payment monitoring)
  async updateDepositStatus(depositId, status, transactionHash = null, confirmations = 0) {
    try {
      const updates = {
        status,
        confirmations,
        updated_at: new Date().toISOString()
      };

      if (transactionHash) {
        updates.transaction_hash = transactionHash;
      }

      if (status === 'detected' && !updates.detected_at) {
        updates.detected_at = new Date().toISOString();
      }

      if (status === 'confirmed' && !updates.confirmed_at) {
        updates.confirmed_at = new Date().toISOString();
      }

      const { data, error } = await supabase
        .from('crypto_deposits')
        .update(updates)
        .eq('id', depositId)
        .select()
        .single();

      if (error) {
        console.error('Error updating deposit status:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in updateDepositStatus:', error);
      return null;
    }
  }

  // Process confirmed deposit (update user balance)
  async processConfirmedDeposit(depositId) {
    try {
      const deposit = await this.getDeposit(depositId);
      if (!deposit || deposit.status !== 'confirmed') {
        return false;
      }

      // Update user balance
      const { error: userError } = await supabase
        .from('users')
        .update({
          balance: supabase.raw(`balance + ${deposit.amount_usd}`),
          updated_at: new Date().toISOString()
        })
        .eq('id', deposit.user_id);

      if (userError) {
        console.error('Error updating user balance:', userError);
        return false;
      }

      // Create transaction record
      const { error: transactionError } = await supabase
        .from('transactions')
        .insert({
          user_id: deposit.user_id,
          type: 'deposit',
          amount: deposit.amount_usd,
          status: 'completed',
          description: `Crypto deposit - ${deposit.crypto_amount} ${deposit.cryptocurrency}`,
          created_at: new Date().toISOString()
        });

      if (transactionError) {
        console.error('Error creating transaction record:', transactionError);
        // Don't return false here as balance was already updated
      }

      return true;
    } catch (error) {
      console.error('Error processing confirmed deposit:', error);
      return false;
    }
  }

  // Check for expired deposits
  async checkExpiredDeposits() {
    try {
      const { data: expiredDeposits, error } = await supabase
        .from('crypto_deposits')
        .update({ status: 'expired' })
        .eq('status', 'pending')
        .lt('expires_at', new Date().toISOString())
        .select();

      if (error) {
        console.error('Error checking expired deposits:', error);
        return [];
      }

      return expiredDeposits || [];
    } catch (error) {
      console.error('Error in checkExpiredDeposits:', error);
      return [];
    }
  }

  // Format deposit for display
  formatDepositForDisplay(deposit) {
    const crypto = cryptoService.getSupportedCryptocurrencies().find(c => c.symbol === deposit.cryptocurrency);
    
    return {
      ...deposit,
      crypto_details: crypto,
      formatted_crypto_amount: cryptoService.formatCryptoAmount(deposit.crypto_amount, crypto?.id),
      formatted_usd_amount: `$${deposit.amount_usd.toFixed(2)}`,
      time_remaining: this.getTimeRemaining(deposit.expires_at),
      status_display: this.getStatusDisplay(deposit.status)
    };
  }

  // Get time remaining for deposit
  getTimeRemaining(expiresAt) {
    const now = new Date();
    const expires = new Date(expiresAt);
    const diff = expires - now;

    if (diff <= 0) return 'Expired';

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m remaining`;
    } else {
      return `${minutes}m remaining`;
    }
  }

  // Get status display text
  getStatusDisplay(status) {
    const statusMap = {
      'pending': 'Waiting for Payment',
      'detected': 'Payment Detected',
      'confirmed': 'Payment Confirmed',
      'failed': 'Payment Failed',
      'expired': 'Expired'
    };

    return statusMap[status] || status;
  }
}

export default new DepositService();
